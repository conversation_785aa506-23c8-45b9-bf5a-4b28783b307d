/* Modern Government Client Header Styles */
:root {
  --gov-blue: #1e3a8a;
  --gov-blue-dark: #1e40af;
  --gov-blue-light: #3b82f6;
  --gov-yellow: #fbbf24;
  --gov-yellow-light: #fef3c7;
  --gov-red: #dc2626;
  --gov-green: #059669;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --bg-white: #ffffff;
  --bg-gray-50: #f9fafb;
  --bg-gray-100: #f3f4f6;
  --border-gray: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
.client-header * {
  box-sizing: border-box;
}

.client-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-white);
  box-shadow: var(--shadow-md);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Government Banner */
.gov-banner {
  background: var(--gov-blue);
  color: white;
  padding: 0.5rem 0;
  font-size: 0.875rem;
}

.gov-banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.gov-banner-flag {
  display: flex;
  align-items: center;
}

.flag-icon {
  width: 20px;
  height: auto;
}

.gov-banner-text {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.gov-banner-label {
  opacity: 0.9;
}

.gov-banner-agency {
  color: var(--gov-yellow);
}

.gov-banner-secure {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Main Header */
.main-header {
  background: var(--bg-white);
  border-bottom: 1px solid var(--border-gray);
  padding: 0.75rem 0;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

/* Logo and Site Identity */
.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
}

.logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.site-identity {
  display: flex;
  flex-direction: column;
}

.site-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gov-blue);
  margin: 0;
  line-height: 1.2;
}

.site-subtitle {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  gap: 4px;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--gov-blue);
  transition: all 0.3s ease;
  border-radius: 1px;
}

.mobile-menu-toggle:hover .hamburger-line {
  background: var(--gov-blue-light);
}

/* Main Navigation */
.main-navigation {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  background: var(--bg-gray-50);
  color: var(--gov-blue);
  text-decoration: none;
}

.nav-link.active {
  background: var(--gov-yellow-light);
  color: var(--gov-blue);
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--gov-blue);
  border-radius: 50%;
}

.nav-link i {
  font-size: 1rem;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

/* Search Container */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-toggle:hover {
  background: var(--bg-gray-50);
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.search-box {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 0.5rem;
  min-width: 300px;
  margin-top: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1001;
}

.search-box.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.search-box {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-gray);
  border-radius: 6px;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: var(--gov-blue);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.search-submit {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--gov-blue);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-submit:hover {
  background: var(--gov-blue-dark);
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-profile.active .dropdown-arrow {
  transform: rotate(180deg);
}

.user-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: var(--text-primary);
}

.user-btn:hover {
  background: var(--bg-gray-50);
  border-color: var(--gov-blue);
  box-shadow: var(--shadow-sm);
}

.user-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 1.5rem;
  color: var(--text-light);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.2;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.user-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-light);
  transition: transform 0.2s ease;
  margin-left: auto;
}

/* User Dropdown Menu */
.user-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  padding: 0.5rem 0;
  min-width: 280px;
  z-index: 1002;
  margin-top: 0.5rem;
  overflow: hidden;
}

.dropdown-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-gray);
  background: var(--bg-gray-50);
}

.user-details strong {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--bg-gray-50);
  color: var(--gov-blue);
}

.dropdown-item i {
  width: 16px;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.dropdown-item:hover i {
  color: var(--gov-blue);
}

.logout-item {
  color: var(--gov-red);
}

.logout-item:hover {
  background: #fef2f2;
  color: var(--gov-red);
}

.logout-item i {
  color: var(--gov-red);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-gray);
  margin: 0.5rem 0;
}

/* Breadcrumb Section */
.breadcrumb-section {
  background: var(--bg-gray-50);
  border-bottom: 1px solid var(--border-gray);
  padding: 0.75rem 0;
}

.breadcrumb-nav {
  font-size: 0.875rem;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--text-light);
  margin-left: 0.5rem;
}

.breadcrumb-item a {
  color: var(--gov-blue);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
  color: var(--gov-blue-dark);
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: var(--text-secondary);
  font-weight: 500;
}

.breadcrumb-item i {
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 1.5rem;
  }

  .main-navigation {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .search-box {
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .client-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .gov-banner-content {
    padding: 0 1rem;
    gap: 0.75rem;
  }

  .gov-banner-text {
    font-size: 0.75rem;
  }

  .gov-banner-secure {
    display: none;
  }

  .main-header {
    padding: 0.5rem 0;
  }

  .header-container {
    padding: 0 1rem;
    gap: 1rem;
  }

  .logo {
    width: 32px;
    height: 32px;
  }

  .site-title {
    font-size: 1rem;
  }

  .site-subtitle {
    font-size: 0.7rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .user-info {
    display: none;
  }

  .user-btn {
    padding: 0.5rem;
    min-width: 40px;
    min-height: 40px;
  }

  .search-toggle {
    width: 36px;
    height: 36px;
  }

  .search-box {
    min-width: 260px;
    right: -1rem;
  }

  .user-dropdown-menu {
    min-width: 240px;
    right: -1rem;
  }

  .breadcrumb-section {
    padding: 0.5rem 0;
  }

  .breadcrumb-list {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .gov-banner {
    padding: 0.375rem 0;
  }

  .gov-banner-content {
    padding: 0 0.75rem;
    gap: 0.5rem;
  }

  .flag-icon {
    width: 16px;
  }

  .gov-banner-text {
    font-size: 0.7rem;
  }

  .gov-banner-agency {
    display: block;
    margin-top: 0.125rem;
  }

  .header-container {
    padding: 0 0.75rem;
    gap: 0.75rem;
  }

  .logo {
    width: 28px;
    height: 28px;
  }

  .site-title {
    font-size: 0.9rem;
  }

  .site-subtitle {
    font-size: 0.65rem;
  }

  .header-actions {
    gap: 0.375rem;
  }

  .search-toggle,
  .user-btn {
    width: 32px;
    height: 32px;
    padding: 0.375rem;
  }

  .search-box {
    min-width: 240px;
    right: -0.75rem;
  }

  .search-input {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .search-submit {
    width: 32px;
    height: 32px;
  }

  .user-dropdown-menu {
    min-width: 200px;
    right: -0.75rem;
  }

  .dropdown-header {
    padding: 0.75rem 1rem;
  }

  .dropdown-item {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }

  .breadcrumb-section {
    padding: 0.375rem 0;
  }

  .breadcrumb-list {
    font-size: 0.7rem;
  }
}

/* Focus and Accessibility Styles */
.search-toggle:focus,
.user-btn:focus,
.mobile-menu-toggle:focus,
.nav-link:focus,
.dropdown-item:focus {
  outline: 2px solid var(--gov-blue);
  outline-offset: 2px;
}

.search-input:focus {
  outline: none;
  border-color: var(--gov-blue);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .client-header *,
  .client-header *::before,
  .client-header *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .client-header {
    border-bottom: 2px solid;
  }

  .nav-link.active {
    background: var(--text-primary);
    color: var(--bg-white);
  }

  .user-btn {
    border: 2px solid;
  }
}

/* Print Styles */
@media print {
  .client-header {
    display: none;
  }
}


