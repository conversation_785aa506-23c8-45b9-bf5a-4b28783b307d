<template>
  <div class="client-dashboard">
    <!-- Background -->
    <div class="background-container">
      <div class="background-image"></div>
      <div class="background-overlay"></div>
    </div>

    <!-- Client Header with Navigation -->
    <ClientHeader
      :userName="userName"
      :userEmail="userEmail"
      :userAvatar="userAvatar"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      :showBreadcrumbs="true"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @error="handleError"
      @search="handleSearch"
    />

    <!-- Main Content -->
    <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- Welcome Section -->
      <section class="welcome-section">
        <div class="container">
          <div class="welcome-header">
            <div class="welcome-text">
              <h1 class="welcome-title">Welcome back, {{ firstName }}!</h1>
              <p class="welcome-subtitle">Request official documents quickly and securely through our digital platform.</p>
            </div>
            <div class="welcome-stats">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ totalRequests }}</div>
                  <div class="stat-label">Total Requests</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ pendingRequests }}</div>
                  <div class="stat-label">Pending</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Quick Actions Section -->
      <section class="quick-actions-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Quick Actions</h2>
            <p class="section-description">Start a new document request or manage existing ones</p>
          </div>

          <div class="quick-actions-grid">
            <div class="action-card primary" @click="scrollToServices">
              <div class="action-icon">
                <i class="fas fa-plus-circle"></i>
              </div>
              <div class="action-content">
                <h3>New Request</h3>
                <p>Start a new document request</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="action-card" @click="goToMyRequests">
              <div class="action-icon">
                <i class="fas fa-list-alt"></i>
              </div>
              <div class="action-content">
                <h3>My Requests</h3>
                <p>View and track your requests</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="action-card" @click="goToProfile">
              <div class="action-icon">
                <i class="fas fa-user-edit"></i>
              </div>
              <div class="action-content">
                <h3>Update Profile</h3>
                <p>Keep your information current</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Document Services Section -->
      <section class="services-section" ref="servicesSection">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Available Document Services</h2>
            <p class="section-description">Choose the type of document you want to request</p>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>Loading available services...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="error-container">
            <div class="error-content">
              <i class="fas fa-exclamation-triangle"></i>
              <h3>Unable to Load Services</h3>
              <p>{{ error }}</p>
              <button class="retry-btn" @click="loadDocumentTypes">
                <i class="fas fa-redo"></i>
                Try Again
              </button>
            </div>
          </div>

          <!-- Document Types Grid -->
          <div v-else class="document-types-grid">
            <div
              v-for="documentType in documentTypes"
              :key="documentType.id"
              class="document-card"
              @click="selectDocumentType(documentType)"
              :class="{ 'disabled': !documentType.is_active }"
            >
              <div class="document-icon">
                <i :class="getDocumentIcon(documentType.type_name)"></i>
              </div>

              <div class="document-content">
                <h3 class="document-title">{{ documentType.type_name }}</h3>
                <p class="document-description">{{ documentType.description }}</p>

                <div class="document-details">
                  <div class="fee-info">
                    <span class="fee-label">Base Fee:</span>
                    <span class="fee-amount">₱{{ formatCurrency(documentType.base_fee) }}</span>
                  </div>

                  <div class="processing-time">
                    <i class="fas fa-clock"></i>
                    <span>{{ getProcessingTime(documentType.type_name) }}</span>
                  </div>
                </div>
              </div>

              <div class="document-action">
                <span v-if="!documentType.is_active" class="status-badge unavailable">
                  Unavailable
                </span>
                <i v-else class="fas fa-chevron-right"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Information Section -->
      <section class="info-section">
        <div class="container">
          <div class="info-grid">
            <div class="info-card">
              <div class="info-header">
                <i class="fas fa-info-circle"></i>
                <h3>Important Information</h3>
              </div>
              <div class="info-content">
                <ul class="info-list">
                  <li>
                    <i class="fas fa-check"></i>
                    Ensure your profile information is complete and accurate
                  </li>
                  <li>
                    <i class="fas fa-check"></i>
                    Have your valid ID and supporting documents ready
                  </li>
                  <li>
                    <i class="fas fa-check"></i>
                    Processing time may vary depending on document verification
                  </li>
                  <li>
                    <i class="fas fa-check"></i>
                    You can pay online using various payment methods
                  </li>
                </ul>
              </div>
            </div>

            <div class="help-card">
              <div class="help-header">
                <i class="fas fa-headset"></i>
                <h3>Need Help?</h3>
              </div>
              <div class="help-content">
                <p>If you have questions about document requirements or the application process:</p>
                <div class="help-actions">
                  <button class="help-btn" @click="openHelp">
                    <i class="fas fa-question-circle"></i>
                    View FAQ
                  </button>
                  <button class="contact-btn" @click="contactSupport">
                    <i class="fas fa-phone"></i>
                    Contact Support
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';
import ClientHeader from './ClientHeader.vue';
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'NewDocumentRequest',
  components: {
    ClientHeader
  },
  data() {
    return {
      documentTypes: [],
      loading: true,
      error: null,
      // Header state
      showUserDropdown: false,
      sidebarCollapsed: false,
      activeMenu: 'dashboard',
      // User data
      userName: 'User',
      userEmail: '<EMAIL>',
      userAvatar: null,
      firstName: 'User',
      totalRequests: 0,
      pendingRequests: 0
    };
  },
  async mounted() {
    await this.loadUserData();
    await this.loadDocumentTypes();
    await this.loadUserStats();
  },
  methods: {
    async loadUserData() {
      try {
        const currentUser = unifiedAuthService.getCurrentUser();
        if (currentUser) {
          this.userName = currentUser.username || 'User';
          this.userEmail = currentUser.email || '<EMAIL>';
          this.firstName = currentUser.first_name || currentUser.username || 'User';
          // Set user avatar if available
          this.userAvatar = currentUser.avatar || null;
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    },

    async loadUserStats() {
      try {
        // TODO: Implement API call to get user statistics
        // For now, using placeholder data
        this.totalRequests = 5;
        this.pendingRequests = 2;
      } catch (error) {
        console.error('Error loading user stats:', error);
      }
    },

    async loadDocumentTypes() {
      try {
        this.loading = true;
        this.error = null;

        const response = await documentRequestService.getDocumentTypes();
        this.documentTypes = response.data || [];

      } catch (error) {
        console.error('Error loading document types:', error);
        this.error = error.response?.data?.message || 'Failed to load available services';
      } finally {
        this.loading = false;
      }
    },

    selectDocumentType(documentType) {
      if (!documentType.is_active) return;

      // Navigate to specific document request form
      const routeName = this.getRouteForDocumentType(documentType.type_name);
      if (routeName) {
        this.$router.push({ 
          name: routeName,
          params: { documentTypeId: documentType.id }
        });
      }
    },

    getRouteForDocumentType(typeName) {
      const routes = {
        'Barangay Clearance': 'BarangayClearanceRequest',
        'Cedula': 'CedulaRequest'
      };
      return routes[typeName];
    },

    getDocumentIcon(typeName) {
      const icons = {
        'Barangay Clearance': 'fas fa-certificate',
        'Cedula': 'fas fa-id-card'
      };
      return icons[typeName] || 'fas fa-file-alt';
    },

    getProcessingTime(typeName) {
      const times = {
        'Barangay Clearance': '3-5 business days',
        'Cedula': '1-2 business days'
      };
      return times[typeName] || '3-5 business days';
    },

    formatCurrency(amount) {
      return parseFloat(amount).toFixed(2);
    },

    // Header event handlers
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      console.log('Menu action:', action);
      switch (action) {
        case 'profile':
          // TODO: Navigate to profile page
          break;
        case 'settings':
          // TODO: Navigate to settings page
          break;
        case 'account':
          // TODO: Navigate to account page
          break;
      }
    },

    handleLogout() {
      try {
        unifiedAuthService.logout();
        this.$router.push({ name: 'WelcomePage' });
      } catch (error) {
        console.error('Logout error:', error);
      }
    },

    handleError(error) {
      console.error('Header error:', error);
    },

    // Search handler
    handleSearch(query) {
      console.log('Search query:', query);
      // TODO: Implement search functionality
      // This could search through documents, services, or requests
      // For now, we'll just log the query
    },

    // Navigation methods
    scrollToServices() {
      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });
    },

    goToMyRequests() {
      this.$router.push({ name: 'MyRequests' });
    },

    goToProfile() {
      // TODO: Navigate to profile page
      console.log('Navigate to profile');
    },

    goBack() {
      this.$router.push({ name: 'ClientDashboard' });
    },

    openHelp() {
      // TODO: Implement help/FAQ modal or page
      console.log('Opening help...');
    },

    contactSupport() {
      // TODO: Implement contact support functionality
      console.log('Contacting support...');
    }
  }
};
</script>

<style scoped>
/* Reset and base styles */
* {
  box-sizing: border-box;
}

.client-dashboard {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Background Setup */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.background-image {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/bula-request-background-pic.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(30, 58, 138, 0.85) 0%,
    rgba(30, 64, 175, 0.9) 100%
  );
  z-index: -1;
}

/* Container utility */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Main Content */
.main-content {
  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

/* Welcome Section */
.welcome-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.welcome-header {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-text {
  color: #1e3a8a;
}

.welcome-title {
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #1e3a8a;
}

.welcome-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.welcome-stats {
  display: flex;
  gap: 1rem;
}

.stat-card {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  border-radius: 1rem;
  padding: 1.5rem;
  color: white;
  text-align: center;
  flex: 1;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.3);
}

.stat-icon {
  font-size: 2rem;
  color: #fbbf24;
  margin-bottom: 0.75rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: #fbbf24;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Quick Actions Section */
.quick-actions-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.section-description {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: #1e3a8a;
}

.action-card.primary {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  color: white;
}

.action-card.primary:hover {
  border-color: #fbbf24;
}

.action-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #1e3a8a;
  flex-shrink: 0;
}

.action-card.primary .action-icon {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: inherit;
}

.action-content p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

.action-arrow {
  color: #6b7280;
  font-size: 1.25rem;
}

.action-card.primary .action-arrow {
  color: rgba(255, 255, 255, 0.8);
}

/* Services Section */
.services-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.services-section .section-title {
  color: white;
}

.services-section .section-description {
  color: rgba(255, 255, 255, 0.9);
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-spinner i {
  font-size: 3rem;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: #dc2626;
  margin-bottom: 1rem;
}

.retry-btn {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
}

.document-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.document-card:hover:not(.disabled) {
  border-color: #1e3a8a;
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);
  background: rgba(255, 255, 255, 1);
}

.document-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.document-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.document-content {
  flex: 1;
}

.document-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 0.5rem;
}

.document-description {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.fee-label {
  color: #9ca3af;
  font-size: 0.9rem;
}

.fee-amount {
  font-weight: 600;
  color: #059669;
  font-size: 1.1rem;
}

.processing-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.document-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.document-action i {
  color: #d1d5db;
  font-size: 1.25rem;
}

.status-badge.unavailable {
  background: #fecaca;
  color: #dc2626;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Info Section */
.info-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.info-card, .help-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.info-card:hover, .help-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);
  background: rgba(255, 255, 255, 1);
  border-color: #1e3a8a;
}

.info-header, .help-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-header h3, .help-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0;
}

.info-header i {
  color: #1e3a8a;
  font-size: 1.25rem;
}

.help-header i {
  color: #059669;
  font-size: 1.25rem;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #6b7280;
  line-height: 1.6;
}

.info-list i {
  color: #059669;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.help-content p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.help-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.help-btn, .contact-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(30, 58, 138, 0.3);
  padding: 0.875rem 1.25rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.95rem;
}

.help-btn:hover {
  border-color: #1e3a8a;
  color: #1e3a8a;
  background: rgba(30, 58, 138, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);
}

.contact-btn:hover {
  border-color: #059669;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }

  .welcome-header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .welcome-stats {
    justify-content: center;
  }

  .stat-card {
    min-width: 120px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .document-types-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .document-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .info-card, .help-card {
    padding: 1.5rem;
  }

  .loading-container, .error-container {
    padding: 3rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .welcome-section,
  .quick-actions-section,
  .services-section,
  .info-section {
    padding: 1.5rem 0;
  }

  .welcome-header {
    padding: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .action-icon {
    width: 3rem;
    height: 3rem;
  }

  .document-card {
    padding: 1rem;
  }

  .info-card, .help-card {
    padding: 1rem;
  }

  .loading-container, .error-container {
    padding: 2rem 1rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-section,
.quick-actions-section,
.services-section,
.info-section {
  animation: fadeInUp 0.8s ease-out;
}

.quick-actions-section {
  animation-delay: 0.2s;
}

.services-section {
  animation-delay: 0.4s;
}

.info-section {
  animation-delay: 0.6s;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }

  .action-card:hover,
  .document-card:hover,
  .info-card:hover,
  .help-card:hover {
    transform: none;
  }
}

/* Focus styles */
.action-card:focus,
.document-card:focus,
.help-btn:focus,
.contact-btn:focus,
.retry-btn:focus {
  outline: 3px solid #fbbf24;
  outline-offset: 2px;
}
</style>
