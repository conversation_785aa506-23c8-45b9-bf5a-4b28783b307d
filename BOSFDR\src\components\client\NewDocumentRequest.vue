<template>
  <div class="client-dashboard">
    <!-- Background -->
    <div class="background-container">
      <div class="background-image"></div>
      <div class="background-overlay"></div>
    </div>

    <!-- Client Header with Navigation -->
    <ClientHeader
      :userName="userName"
      :userEmail="userEmail"
      :userAvatar="userAvatar"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      :showBreadcrumbs="true"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
      @error="handleError"
      @search="handleSearch"
    />

    <!-- Main Content -->
    <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content">
            <div class="hero-text">
              <h1 class="hero-title">Welcome back, {{ firstName }}!</h1>
              <p class="hero-subtitle">Access government services and request official documents through our secure digital platform.</p>
              <div class="hero-actions">
                <button class="btn btn-primary" @click="scrollToServices">
                  <i class="fas fa-plus-circle" aria-hidden="true"></i>
                  Start New Request
                </button>
                <button class="btn btn-secondary" @click="goToMyRequests">
                  <i class="fas fa-list-alt" aria-hidden="true"></i>
                  View My Requests
                </button>
              </div>
            </div>
            <div class="hero-stats">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-file-alt" aria-hidden="true"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ totalRequests }}</div>
                    <div class="stat-label">Total Requests</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-clock" aria-hidden="true"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ pendingRequests }}</div>
                    <div class="stat-label">Pending</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ totalRequests - pendingRequests }}</div>
                    <div class="stat-label">Completed</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Quick Actions Section -->
      <section class="quick-actions-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Quick Actions</h2>
            <p class="section-description">Common tasks and frequently used services</p>
          </div>

          <div class="quick-actions-grid">
            <div class="action-card primary" @click="scrollToServices" role="button" tabindex="0" @keyup.enter="scrollToServices">
              <div class="action-icon">
                <i class="fas fa-plus-circle" aria-hidden="true"></i>
              </div>
              <div class="action-content">
                <h3>New Document Request</h3>
                <p>Start a new request for official documents</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right" aria-hidden="true"></i>
              </div>
            </div>

            <div class="action-card" @click="goToMyRequests" role="button" tabindex="0" @keyup.enter="goToMyRequests">
              <div class="action-icon">
                <i class="fas fa-list-alt" aria-hidden="true"></i>
              </div>
              <div class="action-content">
                <h3>Track Requests</h3>
                <p>View status and track your submitted requests</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right" aria-hidden="true"></i>
              </div>
            </div>

            <div class="action-card" @click="goToProfile" role="button" tabindex="0" @keyup.enter="goToProfile">
              <div class="action-icon">
                <i class="fas fa-user-edit" aria-hidden="true"></i>
              </div>
              <div class="action-content">
                <h3>Update Profile</h3>
                <p>Manage your account and personal information</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right" aria-hidden="true"></i>
              </div>
            </div>

            <div class="action-card" @click="contactSupport" role="button" tabindex="0" @keyup.enter="contactSupport">
              <div class="action-icon">
                <i class="fas fa-headset" aria-hidden="true"></i>
              </div>
              <div class="action-content">
                <h3>Get Support</h3>
                <p>Contact our support team for assistance</p>
              </div>
              <div class="action-arrow">
                <i class="fas fa-arrow-right" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Document Services Section -->
      <section class="services-section" ref="servicesSection">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">Available Document Services</h2>
            <p class="section-description">Select the type of document you need to request</p>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="loading-container" role="status" aria-live="polite">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin" aria-hidden="true"></i>
            </div>
            <p>Loading available services...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="error-container" role="alert">
            <div class="error-content">
              <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
              <h3>Unable to Load Services</h3>
              <p>{{ error }}</p>
              <button class="btn btn-primary retry-btn" @click="loadDocumentTypes">
                <i class="fas fa-redo" aria-hidden="true"></i>
                Try Again
              </button>
            </div>
          </div>

          <!-- Document Types Grid -->
          <div v-else class="document-types-grid">
            <div
              v-for="documentType in documentTypes"
              :key="documentType.id"
              class="document-card"
              @click="selectDocumentType(documentType)"
              @keyup.enter="selectDocumentType(documentType)"
              :class="{ 'disabled': !documentType.is_active }"
              role="button"
              :tabindex="documentType.is_active ? 0 : -1"
              :aria-disabled="!documentType.is_active"
            >
              <div class="document-header">
                <div class="document-icon">
                  <i :class="getDocumentIcon(documentType.type_name)" aria-hidden="true"></i>
                </div>
                <div class="document-status">
                  <span v-if="!documentType.is_active" class="status-badge unavailable">
                    Unavailable
                  </span>
                  <span v-else class="status-badge available">
                    Available
                  </span>
                </div>
              </div>

              <div class="document-content">
                <h3 class="document-title">{{ documentType.type_name }}</h3>
                <p class="document-description">{{ documentType.description }}</p>

                <div class="document-details">
                  <div class="detail-item">
                    <i class="fas fa-peso-sign" aria-hidden="true"></i>
                    <span class="detail-label">Fee:</span>
                    <span class="detail-value fee-amount">₱{{ formatCurrency(documentType.base_fee) }}</span>
                  </div>

                  <div class="detail-item">
                    <i class="fas fa-clock" aria-hidden="true"></i>
                    <span class="detail-label">Processing:</span>
                    <span class="detail-value">{{ getProcessingTime(documentType.type_name) }}</span>
                  </div>
                </div>
              </div>

              <div class="document-action">
                <i v-if="documentType.is_active" class="fas fa-chevron-right" aria-hidden="true"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Information and Help Section -->
      <section class="info-section">
        <div class="container">
          <div class="info-grid">
            <!-- Requirements Card -->
            <div class="info-card requirements-card">
              <div class="card-header">
                <i class="fas fa-clipboard-list" aria-hidden="true"></i>
                <h3>Before You Start</h3>
              </div>
              <div class="card-content">
                <ul class="requirements-list">
                  <li class="requirement-item">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                    <span>Complete and accurate profile information</span>
                  </li>
                  <li class="requirement-item">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                    <span>Valid government-issued ID ready for upload</span>
                  </li>
                  <li class="requirement-item">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                    <span>Supporting documents (if required)</span>
                  </li>
                  <li class="requirement-item">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                    <span>Payment method for processing fees</span>
                  </li>
                </ul>
              </div>
            </div>

            <!-- Process Card -->
            <div class="info-card process-card">
              <div class="card-header">
                <i class="fas fa-route" aria-hidden="true"></i>
                <h3>How It Works</h3>
              </div>
              <div class="card-content">
                <ol class="process-steps">
                  <li class="process-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <strong>Select Document</strong>
                      <span>Choose the document type you need</span>
                    </div>
                  </li>
                  <li class="process-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <strong>Fill Application</strong>
                      <span>Complete the required information</span>
                    </div>
                  </li>
                  <li class="process-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <strong>Submit & Pay</strong>
                      <span>Review and submit with payment</span>
                    </div>
                  </li>
                  <li class="process-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <strong>Track Progress</strong>
                      <span>Monitor your request status</span>
                    </div>
                  </li>
                </ol>
              </div>
            </div>

            <!-- Help Card -->
            <div class="info-card help-card">
              <div class="card-header">
                <i class="fas fa-headset" aria-hidden="true"></i>
                <h3>Need Assistance?</h3>
              </div>
              <div class="card-content">
                <p class="help-description">Our support team is here to help you with any questions about document requirements or the application process.</p>
                <div class="help-actions">
                  <button class="btn btn-outline help-btn" @click="openHelp">
                    <i class="fas fa-question-circle" aria-hidden="true"></i>
                    View FAQ
                  </button>
                  <button class="btn btn-outline contact-btn" @click="contactSupport">
                    <i class="fas fa-phone" aria-hidden="true"></i>
                    Contact Support
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import documentRequestService from '@/services/documentRequestService';
import ClientHeader from './ClientHeader.vue';
import unifiedAuthService from '@/services/unifiedAuthService';

export default {
  name: 'NewDocumentRequest',
  components: {
    ClientHeader
  },
  data() {
    return {
      documentTypes: [],
      loading: true,
      error: null,
      // Header state
      showUserDropdown: false,
      sidebarCollapsed: false,
      activeMenu: 'dashboard',
      // User data
      userName: 'User',
      userEmail: '<EMAIL>',
      userAvatar: null,
      firstName: 'User',
      totalRequests: 0,
      pendingRequests: 0
    };
  },
  async mounted() {
    await this.loadUserData();
    await this.loadDocumentTypes();
    await this.loadUserStats();
  },
  methods: {
    async loadUserData() {
      try {
        const currentUser = unifiedAuthService.getCurrentUser();
        if (currentUser) {
          this.userName = currentUser.username || 'User';
          this.userEmail = currentUser.email || '<EMAIL>';
          this.firstName = currentUser.first_name || currentUser.username || 'User';
          // Set user avatar if available
          this.userAvatar = currentUser.avatar || null;
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    },

    async loadUserStats() {
      try {
        // TODO: Implement API call to get user statistics
        // For now, using placeholder data
        this.totalRequests = 5;
        this.pendingRequests = 2;
      } catch (error) {
        console.error('Error loading user stats:', error);
      }
    },

    async loadDocumentTypes() {
      try {
        this.loading = true;
        this.error = null;

        const response = await documentRequestService.getDocumentTypes();
        this.documentTypes = response.data || [];

      } catch (error) {
        console.error('Error loading document types:', error);
        this.error = error.response?.data?.message || 'Failed to load available services';
      } finally {
        this.loading = false;
      }
    },

    selectDocumentType(documentType) {
      if (!documentType.is_active) return;

      // Navigate to specific document request form
      const routeName = this.getRouteForDocumentType(documentType.type_name);
      if (routeName) {
        this.$router.push({ 
          name: routeName,
          params: { documentTypeId: documentType.id }
        });
      }
    },

    getRouteForDocumentType(typeName) {
      const routes = {
        'Barangay Clearance': 'BarangayClearanceRequest',
        'Cedula': 'CedulaRequest'
      };
      return routes[typeName];
    },

    getDocumentIcon(typeName) {
      const icons = {
        'Barangay Clearance': 'fas fa-certificate',
        'Cedula': 'fas fa-id-card'
      };
      return icons[typeName] || 'fas fa-file-alt';
    },

    getProcessingTime(typeName) {
      const times = {
        'Barangay Clearance': '3-5 business days',
        'Cedula': '1-2 business days'
      };
      return times[typeName] || '3-5 business days';
    },

    formatCurrency(amount) {
      return parseFloat(amount).toFixed(2);
    },

    // Header event handlers
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },

    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    handleMenuAction(action) {
      console.log('Menu action:', action);
      switch (action) {
        case 'profile':
          // TODO: Navigate to profile page
          break;
        case 'settings':
          // TODO: Navigate to settings page
          break;
        case 'account':
          // TODO: Navigate to account page
          break;
      }
    },

    handleLogout() {
      try {
        unifiedAuthService.logout();
        this.$router.push({ name: 'WelcomePage' });
      } catch (error) {
        console.error('Logout error:', error);
      }
    },

    handleError(error) {
      console.error('Header error:', error);
    },

    // Search handler
    handleSearch(query) {
      console.log('Search query:', query);
      // TODO: Implement search functionality
      // This could search through documents, services, or requests
      // For now, we'll just log the query
    },

    // Navigation methods
    scrollToServices() {
      this.$refs.servicesSection?.scrollIntoView({ behavior: 'smooth' });
    },

    goToMyRequests() {
      this.$router.push({ name: 'MyRequests' });
    },

    goToProfile() {
      // TODO: Navigate to profile page
      console.log('Navigate to profile');
    },

    goBack() {
      this.$router.push({ name: 'ClientDashboard' });
    },

    openHelp() {
      // TODO: Implement help/FAQ modal or page
      console.log('Opening help...');
    },

    contactSupport() {
      // TODO: Implement contact support functionality
      console.log('Contacting support...');
    }
  }
};
</script>

<style scoped>
/* Modern Government Portal Styles - USWDS Inspired */
:root {
  /* Government Colors */
  --gov-blue: #005ea2;
  --gov-blue-dark: #0f4c96;
  --gov-blue-light: #2378c3;
  --gov-blue-lighter: #e7f6f8;
  --gov-red: #d63384;
  --gov-green: #00a91c;
  --gov-yellow: #ffbe2e;
  --gov-yellow-light: #fef0cd;

  /* Neutral Colors */
  --text-primary: #1b1b1b;
  --text-secondary: #454545;
  --text-light: #757575;
  --text-white: #ffffff;

  /* Background Colors */
  --bg-white: #ffffff;
  --bg-gray-5: #f9f9f9;
  --bg-gray-10: #f0f0f0;
  --bg-gray-20: #dfe1e2;

  /* Shadows */
  --shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  --shadow-2: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  --shadow-3: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  --shadow-4: 0 8px 16px 0 rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-family-sans: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-serif: 'Merriweather', Georgia, serif;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.25rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.2s ease-in-out;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

.client-dashboard {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--gov-blue-lighter) 0%, var(--bg-white) 100%);
  min-height: 100vh;
}

/* Background Setup */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.background-image {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/bula-request-background-pic.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  opacity: 0.1;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 94, 162, 0.05) 0%,
    rgba(35, 120, 195, 0.1) 100%
  );
  z-index: -1;
}

/* Container utility */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Main Content */
.main-content {
  margin-top: 140px; /* Account for new fixed header (gov banner + main header + breadcrumb) */
  transition: margin-left var(--transition-base);
}

.main-content.sidebar-collapsed {
  margin-left: 0;
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-family: var(--font-family-sans);
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--gov-yellow);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--gov-blue);
  color: var(--text-white);
  border-color: var(--gov-blue);
}

.btn-primary:hover {
  background-color: var(--gov-blue-dark);
  border-color: var(--gov-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.btn-secondary {
  background-color: var(--bg-white);
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.btn-secondary:hover {
  background-color: var(--gov-blue);
  color: var(--text-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.btn-outline {
  background-color: transparent;
  color: var(--gov-blue);
  border-color: var(--gov-blue);
}

.btn-outline:hover {
  background-color: var(--gov-blue);
  color: var(--text-white);
}

/* Hero Section */
.hero-section {
  padding: var(--spacing-16) 0 var(--spacing-12);
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-12);
  align-items: center;
  background: var(--bg-white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-12);
  box-shadow: var(--shadow-4);
  border: 1px solid var(--bg-gray-20);
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  color: var(--gov-blue);
  margin: 0;
  line-height: 1.1;
  font-family: var(--font-family-serif);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  max-width: 90%;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.hero-stats {
  display: flex;
  justify-content: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-4);
  width: 100%;
}

.stat-card {
  background: linear-gradient(135deg, var(--gov-blue), var(--gov-blue-light));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  color: var(--text-white);
  text-align: center;
  flex: 1;
  box-shadow: var(--shadow-3);
  transition: all var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-4);
}

.stat-icon {
  font-size: 2rem;
  color: var(--gov-yellow);
  margin-bottom: var(--spacing-3);
  display: block;
}

.stat-number {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: var(--spacing-1);
  color: var(--gov-yellow);
  font-family: var(--font-family-serif);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  font-weight: 500;
  line-height: 1.2;
}

/* Quick Actions Section */
.quick-actions-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.section-description {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: #1e3a8a;
}

.action-card.primary {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  color: white;
}

.action-card.primary:hover {
  border-color: #fbbf24;
}

.action-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #1e3a8a;
  flex-shrink: 0;
}

.action-card.primary .action-icon {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: inherit;
}

.action-content p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

.action-arrow {
  color: #6b7280;
  font-size: 1.25rem;
}

.action-card.primary .action-arrow {
  color: rgba(255, 255, 255, 0.8);
}

/* Services Section */
.services-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.services-section .section-title {
  color: white;
}

.services-section .section-description {
  color: rgba(255, 255, 255, 0.9);
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-spinner i {
  font-size: 3rem;
  color: #1e3a8a;
  margin-bottom: 1rem;
}

.error-content {
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: #dc2626;
  margin-bottom: 1rem;
}

.retry-btn {
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
}

.document-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.document-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.document-card:hover:not(.disabled) {
  border-color: #1e3a8a;
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(30, 58, 138, 0.2);
  background: rgba(255, 255, 255, 1);
}

.document-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.document-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1e3a8a, #1e40af);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.document-content {
  flex: 1;
}

.document-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin-bottom: 0.5rem;
}

.document-description {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.document-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fee-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.fee-label {
  color: #9ca3af;
  font-size: 0.9rem;
}

.fee-amount {
  font-weight: 600;
  color: #059669;
  font-size: 1.1rem;
}

.processing-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
}

.document-action {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.document-action i {
  color: #d1d5db;
  font-size: 1.25rem;
}

.status-badge.unavailable {
  background: #fecaca;
  color: #dc2626;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Info Section */
.info-section {
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.info-card, .help-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(30, 58, 138, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.info-card:hover, .help-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(30, 58, 138, 0.15);
  background: rgba(255, 255, 255, 1);
  border-color: #1e3a8a;
}

.info-header, .help-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-header h3, .help-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0;
}

.info-header i {
  color: #1e3a8a;
  font-size: 1.25rem;
}

.help-header i {
  color: #059669;
  font-size: 1.25rem;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
  color: #6b7280;
  line-height: 1.6;
}

.info-list i {
  color: #059669;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.help-content p {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.help-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.help-btn, .contact-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(30, 58, 138, 0.3);
  padding: 0.875rem 1.25rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.95rem;
}

.help-btn:hover {
  border-color: #1e3a8a;
  color: #1e3a8a;
  background: rgba(30, 58, 138, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);
}

.contact-btn:hover {
  border-color: #059669;
  color: #059669;
  background: rgba(5, 150, 105, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }

  .welcome-header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .welcome-stats {
    justify-content: center;
  }

  .stat-card {
    min-width: 120px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .document-types-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .document-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .info-card, .help-card {
    padding: 1.5rem;
  }

  .loading-container, .error-container {
    padding: 3rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .welcome-section,
  .quick-actions-section,
  .services-section,
  .info-section {
    padding: 1.5rem 0;
  }

  .welcome-header {
    padding: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .action-icon {
    width: 3rem;
    height: 3rem;
  }

  .document-card {
    padding: 1rem;
  }

  .info-card, .help-card {
    padding: 1rem;
  }

  .loading-container, .error-container {
    padding: 2rem 1rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-section,
.quick-actions-section,
.services-section,
.info-section {
  animation: fadeInUp 0.8s ease-out;
}

.quick-actions-section {
  animation-delay: 0.2s;
}

.services-section {
  animation-delay: 0.4s;
}

.info-section {
  animation-delay: 0.6s;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }

  .action-card:hover,
  .document-card:hover,
  .info-card:hover,
  .help-card:hover {
    transform: none;
  }
}

/* Focus styles */
.action-card:focus,
.document-card:focus,
.help-btn:focus,
.contact-btn:focus,
.retry-btn:focus {
  outline: 3px solid #fbbf24;
  outline-offset: 2px;
}
</style>
