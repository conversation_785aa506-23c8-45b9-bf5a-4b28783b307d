<template>
  <header class="client-header" role="banner">
    <!-- Government Banner -->
    <div class="gov-banner">
      <div class="gov-banner-content">
        <div class="gov-banner-flag">
          <img src="/assets/images/ph_flag_small.png" alt="Philippine flag" class="flag-icon" />
        </div>
        <div class="gov-banner-text">
          <span class="gov-banner-label">An official website of the</span>
          <strong class="gov-banner-agency">Barangay Bula, General Santos City</strong>
        </div>
        <div class="gov-banner-secure">
          <i class="fas fa-shield-alt"></i>
          <span>Secure</span>
        </div>
      </div>
    </div>

    <!-- Main Header -->
    <div class="main-header">
      <div class="header-container">
        <!-- Left Section: Logo and Navigation -->
        <div class="header-left">
          <div class="logo-section" @click="handleMenuAction('dashboard')" role="button" tabindex="0" @keyup.enter="handleMenuAction('dashboard')">
            <img src="/assets/images/barangay-logo.png" alt="Barangay Bula Logo" class="logo" />
            <div class="site-identity">
              <h1 class="site-title">Barangay Bula</h1>
              <span class="site-subtitle">Digital Services Portal</span>
            </div>
          </div>

          <!-- Mobile Menu Toggle -->
          <button class="mobile-menu-toggle" @click="handleSidebarToggle" aria-label="Toggle navigation menu" aria-expanded="false">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </button>
        </div>

        <!-- Center Section: Navigation (Desktop) -->
        <nav class="main-navigation" role="navigation" aria-label="Main navigation">
          <ul class="nav-list">
            <li class="nav-item">
              <a href="#" class="nav-link" :class="{ active: activeMenu === 'dashboard' }" @click="handleMenuAction('dashboard')" role="menuitem">
                <i class="fas fa-home" aria-hidden="true"></i>
                <span>Dashboard</span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link" :class="{ active: activeMenu === 'services' }" @click="handleMenuAction('services')" role="menuitem">
                <i class="fas fa-file-alt" aria-hidden="true"></i>
                <span>Services</span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link" :class="{ active: activeMenu === 'requests' }" @click="handleMenuAction('requests')" role="menuitem">
                <i class="fas fa-clock" aria-hidden="true"></i>
                <span>My Requests</span>
              </a>
            </li>
            <li class="nav-item">
              <a href="#" class="nav-link" :class="{ active: activeMenu === 'help' }" @click="handleMenuAction('help')" role="menuitem">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
                <span>Help</span>
              </a>
            </li>
          </ul>
        </nav>

        <!-- Right Section: User Actions -->
        <div class="header-actions">
          <!-- Search -->
          <div class="search-container">
            <button class="search-toggle" @click="toggleSearch" aria-label="Search documents and services" :aria-expanded="showSearch">
              <i class="fas fa-search" aria-hidden="true"></i>
            </button>
            <div class="search-box" :class="{ active: showSearch }" role="search">
              <label for="header-search" class="sr-only">Search documents and services</label>
              <input
                id="header-search"
                type="search"
                placeholder="Search documents, services..."
                class="search-input"
                v-model="searchQuery"
                @keyup.enter="performSearch"
                autocomplete="off"
              />
              <button class="search-submit" @click="performSearch" aria-label="Submit search">
                <i class="fas fa-search" aria-hidden="true"></i>
              </button>
            </div>
          </div>

          <!-- Notifications -->
          <ClientNotifications
            @new-notification="handleNewNotification"
            @notification-click="handleNotificationClick"
            @error="handleNotificationError"
          />

          <!-- User Profile -->
          <div class="user-profile" :class="{ active: showUserDropdown }">
            <button class="user-btn" @click="handleUserDropdownToggle" aria-label="User account menu" :aria-expanded="showUserDropdown">
              <div class="user-avatar">
                <img v-if="userAvatar" :src="userAvatar" :alt="userName" class="avatar-image" />
                <i v-else class="fas fa-user-circle avatar-icon" aria-hidden="true"></i>
              </div>
              <div class="user-info">
                <span class="user-name">{{ userName }}</span>
                <span class="user-role">Client Portal</span>
              </div>
              <i class="fas fa-chevron-down dropdown-arrow" aria-hidden="true"></i>
            </button>

            <div v-if="showUserDropdown" class="user-dropdown-menu" role="menu" aria-label="User account options">
              <div class="dropdown-header">
                <div class="user-details">
                  <strong>{{ userName }}</strong>
                  <span class="user-email">{{ userEmail }}</span>
                </div>
              </div>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click="handleMenuAction('profile')" role="menuitem">
                <i class="fas fa-user" aria-hidden="true"></i>
                <span>My Profile</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('settings')" role="menuitem">
                <i class="fas fa-cog" aria-hidden="true"></i>
                <span>Account Settings</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('documents')" role="menuitem">
                <i class="fas fa-folder" aria-hidden="true"></i>
                <span>My Documents</span>
              </a>
              <a href="#" class="dropdown-item" @click="handleMenuAction('history')" role="menuitem">
                <i class="fas fa-history" aria-hidden="true"></i>
                <span>Request History</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click="handleMenuAction('help')" role="menuitem">
                <i class="fas fa-question-circle" aria-hidden="true"></i>
                <span>Help & Support</span>
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item logout-item" @click="handleLogout" role="menuitem">
                <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                <span>Sign Out</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-section" v-if="showBreadcrumbs">
      <div class="header-container">
        <nav class="breadcrumb-nav" aria-label="Breadcrumb navigation">
          <ol class="breadcrumb-list">
            <li class="breadcrumb-item">
              <a href="#" @click="handleMenuAction('dashboard')">
                <i class="fas fa-home" aria-hidden="true"></i>
                Dashboard
              </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              {{ getPageTitle() }}
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </header>
</template>

<script>
import ClientNotifications from './ClientNotifications.vue';

export default {
  name: 'ClientHeader',
  components: {
    ClientNotifications
  },
  props: {
    userName: {
      type: String,
      default: 'User'
    },
    userEmail: {
      type: String,
      default: '<EMAIL>'
    },
    userAvatar: {
      type: String,
      default: null
    },
    showUserDropdown: {
      type: Boolean,
      default: false
    },
    sidebarCollapsed: {
      type: Boolean,
      default: false
    },
    activeMenu: {
      type: String,
      default: 'dashboard'
    },
    showBreadcrumbs: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      showSearch: false,
      searchQuery: ''
    };
  },

  emits: [
    'sidebar-toggle',
    'user-dropdown-toggle',
    'menu-action',
    'logout',
    'error',
    'search',
    'notification-click'
  ],

  mounted() {
    // Setup event listeners for outside clicks
    document.addEventListener('click', this.handleOutsideClick);
  },

  beforeUnmount() {
    // Clean up event listeners
    document.removeEventListener('click', this.handleOutsideClick);
  },

  methods: {
    // Get page title based on active menu
    getPageTitle() {
      const titles = {
        'dashboard': 'Dashboard',
        'services': 'Document Services',
        'requests': 'My Requests',
        'documents': 'My Documents',
        'profile': 'My Profile',
        'settings': 'Account Settings',
        'history': 'Request History',
        'notifications': 'Notifications',
        'help': 'Help & Support'
      };
      return titles[this.activeMenu] || 'Dashboard';
    },

    // Toggle search functionality
    toggleSearch() {
      this.showSearch = !this.showSearch;
      if (this.showSearch) {
        this.$nextTick(() => {
          const searchInput = this.$el.querySelector('.search-input');
          if (searchInput) {
            searchInput.focus();
          }
        });
      }
    },

    // Perform search
    performSearch() {
      if (this.searchQuery.trim()) {
        this.$emit('search', this.searchQuery.trim());
        // Close search on mobile after search
        if (window.innerWidth <= 768) {
          this.showSearch = false;
        }
      }
    },

    // Handle sidebar toggle
    handleSidebarToggle() {
      this.$emit('sidebar-toggle');
    },

    // Handle user dropdown toggle
    handleUserDropdownToggle() {
      this.$emit('user-dropdown-toggle');
    },

    // Handle menu actions (profile, settings, etc.)
    handleMenuAction(action) {
      this.$emit('menu-action', action);
    },

    // Handle logout
    handleLogout() {
      this.$emit('logout');
    },

    // Handle outside clicks to close dropdowns
    handleOutsideClick(event) {
      // Check if click is outside user dropdown
      if (!event.target.closest('.user-profile')) {
        if (this.showUserDropdown) {
          this.$emit('user-dropdown-toggle');
        }
      }

      // Check if click is outside search
      if (!event.target.closest('.search-container')) {
        this.showSearch = false;
      }
    },

    // Notification event handlers
    handleNewNotification(notification) {
      console.log('New notification received:', notification);
      // Handle new notification - could show toast, update UI, etc.
    },

    async handleNotificationClick(notification) {
      console.log('🔔 ClientHeader: Notification clicked:', notification);

      // Ensure we have a valid notification object
      if (!notification || typeof notification !== 'object') {
        console.error('Invalid notification object received:', notification);
        return;
      }

      try {
        // The ClientNotifications component now handles navigation internally,
        // but we can add additional logic here if needed

        // Parse notification data
        const notificationData = typeof notification.data === 'string'
          ? JSON.parse(notification.data)
          : notification.data || {};

        // Log navigation for debugging
        console.log('📊 ClientHeader: Notification data:', notificationData);

        // Additional header-specific logic can go here
        // For example, updating header state, showing badges, etc.

        // The navigation is now handled by the ClientNotifications component
        // This handler can focus on header-specific updates

      } catch (error) {
        console.error('❌ ClientHeader: Error handling notification click:', error);
      }

      // Always emit the event for other components that might need it
      this.$emit('notification-click', notification);
    },

    handleNotificationError(error) {
      console.error('Notification error:', error);
      this.$emit('error', error);
    }
  }
};
</script>

<style scoped src="./css/clientHeader.css"></style>
